.conversation-item {
  // 使用CSS变量，如果没有设置则使用默认值
  padding: var(--conversation-item-padding, 14px 10px);
  margin-right: var(--conversation-item-margin-right, 20px);
  border-radius: var(--conversation-item-border-radius, 8px);
  background-color: var(--conversation-item-background-color, transparent);
  color: var(--conversation-item-color, inherit);
  cursor: pointer;
  transition: background-color 0.2s ease;

  & + & {
    margin-top: 4px;
  }

  &.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    color: #c0c4cc;
  }

  &.active {
    background-color: var(--conversation-item-active-background-color, #f0f0f0);
    color: var(--conversation-item-active-color, inherit);
    padding: var(--conversation-item-active-padding, 14px 10px);
    margin-right: var(--conversation-item-active-margin-right, 20px);
    border-radius: var(--conversation-item-active-border-radius, 8px);
  }

  &.hovered,
  &:hover {
    background-color: var(--conversation-item-hover-background-color, #f0f0f0);
    color: var(--conversation-item-hover-color, inherit);
    padding: var(--conversation-item-hover-padding, 14px 10px);
    margin-right: var(--conversation-item-hover-margin-right, 20px);
    border-radius: var(--conversation-item-hover-border-radius, 8px);
  }

  &.menu-opened {
    background-color: var(--conversation-item-menu-opened-background-color, #f0f0f0);
    color: var(--conversation-item-menu-opened-color, inherit);
    padding: var(--conversation-item-menu-opened-padding, 14px 10px);
    margin-right: var(--conversation-item-menu-opened-margin-right, 20px);
    border-radius: var(--conversation-item-menu-opened-border-radius, 8px);
  }
}

.conversation-content {
  display: flex;
  align-items: center;
  height: var(--conversation-label-height, 20px);

  .conversation-prefix-icon {
    margin-right: 8px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .conversation-content-main {
    flex: 1;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
  }

  .conversation-label-container {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
    overflow: hidden;
  }

  .conversation-label {
    font-size: 14px;
    color: #303133;
    position: relative;
    white-space: nowrap;
    max-width: var(--conversation-label-max-width, none);
    overflow: hidden;

    &.text-gradient {
      mask-image: linear-gradient(to right, black 60%, transparent 100%);
      -webkit-mask-image: linear-gradient(to right, black 60%, transparent 100%);
    }
  }

  .conversation-timestamp {
    font-size: 14px;
    color: #909399;
    margin-left: 8px;
  }

  .conversation-suffix-icon {
    margin-left: 8px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .conversation-dropdown-more {
    justify-self: center;
    height: 100%;
    display: flex;
    align-items: center;
  }

  .conversation-dropdown-more-icon {
    font-size: 16px;
    padding: 2px;
    border-radius: 5px;
    &:hover {
      background-color: #d3d3d3;
    }
  }

  .conversation-menu {
    margin-left: 8px;
    display: flex;
    align-items: center;
    opacity: 0;
    transition: opacity 0.2s ease;

    .hovered &,
    .active & {
      opacity: 1;
    }
  }
}

// 菜单样式支持CSS变量
.conversation-dropdown-menu {
  background-color: var(--conversation-menu-background-color, #fff);
  border: var(--conversation-menu-border, 1px solid #e4e7ed);
  border-radius: var(--conversation-menu-border-radius, 4px);
  box-shadow: var(--conversation-menu-box-shadow, 0 2px 12px 0 rgba(0, 0, 0, 0.1));
  padding: var(--conversation-menu-padding, 5px 0);
  margin: var(--conversation-menu-margin, 0);
  max-height: var(--conversation-menu-max-height, none);
  overflow: var(--conversation-menu-overflow, visible);
}

.conversation-dropdown-item {
  color: var(--conversation-menu-item-color, #606266);
  background-color: var(--conversation-menu-item-background-color, transparent);
  padding: var(--conversation-menu-item-padding, 0 20px);
  font-size: var(--conversation-menu-item-font-size, 14px);
}
